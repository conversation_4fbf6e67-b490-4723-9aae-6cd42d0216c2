# 🧙‍♂️ Čarodejnícka Alchymistická Hra - Character Animator Puppet

## 📋 Kompletný návod na vytvorenie animovaného čarodejníka

### 🎯 Čo obsahuje tento balík:

1. **wizard_puppet.svg** - Kompletný SVG čarodejník s vrstvami
2. **create_wizard_puppet.jsx** - Photoshop skript pre prípravu vrstiev
3. **setup_wizard_animation.jsx** - After Effects skript pre animácie
4. **Tento návod** - Krok za krokom inštrukcie

---

## 🚀 RÝCHLY ŠTART

### Krok 1: Príprava v Photoshop
```
1. Otvorte Photoshop
2. File > Scripts > Browse... > vyberte "create_wizard_puppet.jsx"
3. Skript vytvorí štruktúru vrstiev
4. Importujte váš PNG obrázok čarodejníka
5. Rozdeľte obrázok na časti podľa vytvorených vrstiev
6. <PERSON><PERSON><PERSON><PERSON> ako .psd súbor
```

### Krok 2: Character Animator
```
1. <PERSON>tvor<PERSON> Adobe Character Animator
2. File > Import > vyberte v<PERSON> .psd súbor
3. Nastavte anchor pointy (pivot body)
4. Pridajte tagy pre automatické správanie
5. Otestujte puppet pomocou webkamery
```

### Krok 3: After Effects (voliteľné)
```
1. Otvorte After Effects
2. File > Scripts > Run Script File... > "setup_wizard_animation.jsx"
3. Importujte Character Animator puppet
4. Použite vytvorené animačné presets
```

---

## 🎨 DETAILNÝ NÁVOD

### 📁 Štruktúra vrstiev pre Character Animator

```
📂 Wizard_Puppet.psd
├── 📁 +Head (pohyblivá hlava)
│   ├── 🖼️ Face (základná tvár)
│   ├── 🖼️ Hair (vlasy)
│   ├── 🖼️ Ears (uši)
│   ├── 📁 +Left_Eye
│   │   ├── 🖼️ Left_Eye_Open
│   │   ├── 🖼️ Left_Eye_Closed
│   │   └── 🖼️ Left_Eye_Blink
│   ├── 📁 +Right_Eye
│   │   ├── 🖼️ Right_Eye_Open
│   │   ├── 🖼️ Right_Eye_Closed
│   │   └── 🖼️ Right_Eye_Blink
│   ├── 📁 Mouth
│   │   ├── 🖼️ Mouth_Neutral
│   │   ├── 🖼️ Mouth_Smile
│   │   ├── 🖼️ Mouth_Frown
│   │   ├── 🖼️ Mouth_Open
│   │   └── 🖼️ Mouth_Surprised
│   ├── 🖼️ Left_Eyebrow
│   ├── 🖼️ Right_Eyebrow
│   ├── 🖼️ Nose
│   ├── 🖼️ Beard
│   └── 🖼️ Mustache
├── 📁 +Body (pohyblivé telo)
│   ├── 🖼️ Torso
│   ├── 🖼️ Robe_Main
│   ├── 🖼️ Robe_Details
│   ├── 🖼️ Hood
│   ├── 🖼️ Amulet
│   └── 🖼️ Belt
├── 📁 +Left_Arm (ľavá ruka)
│   ├── 🖼️ Left_Upper_Arm
│   └── 📁 +Left_Forearm
│       └── 🖼️ Left_Lower_Arm
├── 📁 +Right_Arm (pravá ruka)
│   ├── 🖼️ Right_Upper_Arm
│   └── 📁 +Right_Forearm
│       └── 🖼️ Right_Lower_Arm
├── 📁 +Left_Hand (ľavá dlaň)
│   ├── 🖼️ Left_Palm
│   ├── 🖼️ Left_Fingers
│   └── 🖼️ Left_Ring
└── 📁 +Right_Hand (pravá dlaň)
    ├── 🖼️ Right_Palm
    └── 🖼️ Right_Fingers
```

### 🎯 Anchor Points (Pivot Body)

**DÔLEŽITÉ:** Nastavte anchor pointy v Character Animator:

- **+Head**: Spodok hlavy (krk)
- **+Left_Arm**: Vrchol ramena
- **+Right_Arm**: Vrchol ramena  
- **+Left_Forearm**: Lakeť
- **+Right_Forearm**: Lakeť
- **+Left_Hand**: Zápästie
- **+Right_Hand**: Zápästie

### 🏷️ Tagy pre automatické správanie

Pridajte tieto tagy v Character Animator:

1. **Dýchanie**: `+Body` → Tag: "Breathe"
2. **Mrmlanie**: `+Left_Eye`, `+Right_Eye` → Tag: "Blink"
3. **Sledovanie myši**: `+Left_Eye`, `+Right_Eye` → Tag: "Eye Gaze"
4. **Pohyb hlavy**: `+Head` → Tag: "Head Turner"
5. **Gestikulácia**: `+Left_Arm`, `+Right_Arm` → Tag: "Gesticulate"
6. **Lip Sync**: `Mouth` → Tag: "Lip Sync"

---

## 🎬 ANIMAČNÉ PRESETS

### 1. 😴 Idle Animation (Pokojový stav)
- Jemné dýchanie
- Občasné mrmlanie
- Mierny pohyb hlavy
- Trvanie: 5 sekúnd, loop

### 2. ✨ Spell Casting (Čarovanie)
- Dramatické gestá rukami
- Magické častice
- Žiariace oči
- Intenzívny výraz
- Trvanie: 3 sekundy

### 3. 💬 Talking (Rozhovor)
- Lip sync pripravený
- Pohyby hlavy
- Očný kontakt
- Gestá rukami
- Trvanie: 4 sekundy

### 4. 🔮 Magic Ritual (Magický rituál)
- Komplexné gestá
- Svetelné efekty
- Dramatické pozovanie
- Trvanie: 6 sekúnd

---

## 🎮 INTEGRÁCIA DO HRY

### Export formáty:

**Pre web/hru:**
- Format: H.264/MP4
- Rozlíšenie: 1920x1080 alebo 1280x720
- Frame Rate: 30 fps
- Bitrate: 5-10 Mbps

**Pre vysokú kvalitu:**
- Format: ProRes 422
- Rozlíšenie: 1920x1080
- Frame Rate: 30 fps

**Pre GIF:**
- Format: Animated GIF
- Rozlíšenie: 640x360
- Frame Rate: 15 fps
- Loop: Continuous

### Implementácia v hre:

```javascript
// Príklad použitia v hre
const wizardAnimations = {
    idle: 'wizard_idle.mp4',
    casting: 'wizard_spell.mp4',
    talking: 'wizard_talk.mp4',
    ritual: 'wizard_ritual.mp4'
};

function playWizardAnimation(action) {
    const video = document.getElementById('wizard-character');
    video.src = wizardAnimations[action];
    video.play();
}
```

---

## 🛠️ RIEŠENIE PROBLÉMOV

### ❌ Časté chyby:

1. **Puppet sa nepohybuje správne**
   - Skontrolujte anchor pointy
   - Overte štruktúru vrstiev (+ prefix)

2. **Oči sa nesledujú myš**
   - Pridajte "Eye Gaze" tag
   - Skontrolujte názvy vrstiev očí

3. **Lip sync nefunguje**
   - Overte "Lip Sync" tag na Mouth skupine
   - Vytvorte rôzne výrazy úst

4. **Ruky sa nepohybujú**
   - Skontrolujte hierarchiu: Arm > Forearm > Hand
   - Nastavte správne anchor pointy

### ✅ Tipy pre lepšie výsledky:

- Vytvorte viac výrazov úst pre lepší lip sync
- Pridajte jemné tieňovanie pre hĺbku
- Použite mäkké prechody medzi vrstvami
- Otestujte puppet pred finálnym exportom

---

## 📞 PODPORA

Ak máte problémy:
1. Skontrolujte tento návod
2. Overte štruktúru vrstiev
3. Otestujte v Character Animator
4. Skúste znovu exportovať

**Úspešné čarovanie! 🧙‍♂️✨**
