<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧙‍♂️ Čaro<PERSON><PERSON><PERSON><PERSON><PERSON> Alchymistick<PERSON> Hra - Character Demo</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: white;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }

        h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            background: linear-gradient(45deg, #ffd700, #ffb347);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 1.2em;
            margin-bottom: 30px;
            opacity: 0.8;
        }

        .wizard-container {
            position: relative;
            display: inline-block;
            margin: 20px;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }

        .wizard-display {
            width: 400px;
            height: 500px;
            border: 2px solid #ffd700;
            border-radius: 10px;
            background: radial-gradient(circle at center, #2a2a4a, #1a1a2e);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .wizard-svg {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .controls {
            margin-top: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }

        .btn {
            padding: 12px 20px;
            background: linear-gradient(45deg, #4a90e2, #357abd);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn:hover {
            background: linear-gradient(45deg, #357abd, #2968a3);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(74, 144, 226, 0.4);
        }

        .btn.active {
            background: linear-gradient(45deg, #ffd700, #ffb347);
            color: #1a1a2e;
        }

        .info-panel {
            margin-top: 30px;
            padding: 20px;
            background: rgba(255,255,255,0.05);
            border-radius: 10px;
            border-left: 4px solid #ffd700;
        }

        .status {
            font-size: 1.1em;
            margin-bottom: 10px;
            color: #ffd700;
        }

        .description {
            opacity: 0.8;
            line-height: 1.6;
        }

        .magic-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #ffd700;
            border-radius: 50%;
            animation: float 3s infinite ease-in-out;
            opacity: 0;
        }

        @keyframes float {
            0% { opacity: 0; transform: translateY(100px) scale(0); }
            50% { opacity: 1; transform: translateY(-50px) scale(1); }
            100% { opacity: 0; transform: translateY(-200px) scale(0); }
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            padding: 20px;
            background: rgba(255,255,255,0.05);
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.1);
        }

        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .code-example {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            border-left: 3px solid #ffd700;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧙‍♂️ Čarodejnícka Alchymistická Hra</h1>
        <p class="subtitle">Character Animator Puppet Demo</p>

        <div class="wizard-container">
            <div class="wizard-display" id="wizardDisplay">
                <div class="magic-particles" id="particles"></div>
                <object class="wizard-svg" data="wizard_puppet.svg" type="image/svg+xml" id="wizardSvg">
                    <div style="color: #ffd700; font-size: 18px;">
                        🧙‍♂️<br>
                        Načítavam čarodejníka...<br>
                        <small>Uistite sa, že máte wizard_puppet.svg v rovnakom priečinku</small>
                    </div>
                </object>
            </div>

            <div class="controls">
                <button class="btn active" onclick="setAnimation('idle')">😴 Pokojový stav</button>
                <button class="btn" onclick="setAnimation('casting')">✨ Čarovanie</button>
                <button class="btn" onclick="setAnimation('talking')">💬 Rozhovor</button>
                <button class="btn" onclick="setAnimation('ritual')">🔮 Rituál</button>
                <button class="btn" onclick="toggleMagic()">⚡ Magia</button>
                <button class="btn" onclick="randomGesture()">🤲 Gestá</button>
            </div>

            <div class="info-panel">
                <div class="status" id="status">Stav: Pokojový stav</div>
                <div class="description" id="description">
                    Čarodejník je v pokojovom stave. Jemne dýcha a občas mrmle očami.
                </div>
            </div>
        </div>

        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">🎭</div>
                <h3>Character Animator Ready</h3>
                <p>Kompletná štruktúra vrstiev pripravená pre Adobe Character Animator s automatickými tagmi.</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🎨</div>
                <h3>Photoshop Integration</h3>
                <p>Automatický skript pre vytvorenie správnej hierarchie vrstiev a anchor pointov.</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🎬</div>
                <h3>After Effects Presets</h3>
                <p>Predpripravené animačné kompozície s magickými efektmi a svetelnými animáciami.</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🎮</div>
                <h3>Game Ready</h3>
                <p>Optimalizované pre integráciu do hier s rôznymi exportnými formátmi.</p>
                <div class="code-example">
// Príklad použitia v hre
const wizard = new WizardCharacter();
wizard.playAnimation('casting');
wizard.speak("Abrakadabra!");
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentAnimation = 'idle';
        let magicActive = false;

        const animations = {
            idle: {
                name: 'Pokojový stav',
                description: 'Čarodejník je v pokojovom stave. Jemne dýcha a občas mrmle očami.',
                icon: '😴'
            },
            casting: {
                name: 'Čarovanie',
                description: 'Čarodejník vykonává kúzlo s dramatickými gestami a magickými efektmi.',
                icon: '✨'
            },
            talking: {
                name: 'Rozhovor',
                description: 'Čarodejník hovorí s pohybmi hlavy a gestami rukami. Pripravený pre lip sync.',
                icon: '💬'
            },
            ritual: {
                name: 'Magický rituál',
                description: 'Komplexný magický rituál s intenzívnymi svetelnými efektmi.',
                icon: '🔮'
            }
        };

        function setAnimation(type) {
            currentAnimation = type;
            
            // Aktualizácia UI
            document.querySelectorAll('.btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // Aktualizácia statusu
            const animation = animations[type];
            document.getElementById('status').textContent = `Stav: ${animation.name}`;
            document.getElementById('description').textContent = animation.description;
            
            // Simulácia animácie (v skutočnej implementácii by sa zmenil video/animácia)
            console.log(`Playing animation: ${type}`);
            
            // Pridanie vizuálnych efektov
            if (type === 'casting' || type === 'ritual') {
                createMagicParticles();
            }
        }

        function toggleMagic() {
            magicActive = !magicActive;
            
            if (magicActive) {
                createMagicParticles();
                document.getElementById('status').textContent = 'Stav: Magické efekty aktívne';
                event.target.style.background = 'linear-gradient(45deg, #ffd700, #ffb347)';
                event.target.style.color = '#1a1a2e';
            } else {
                clearMagicParticles();
                setAnimation(currentAnimation);
                event.target.style.background = '';
                event.target.style.color = '';
            }
        }

        function randomGesture() {
            const gestures = ['👋', '🤲', '👐', '🙌', '👆', '✋'];
            const randomGesture = gestures[Math.floor(Math.random() * gestures.length)];
            
            document.getElementById('status').textContent = `Gesto: ${randomGesture}`;
            
            // Simulácia gesta
            setTimeout(() => {
                setAnimation(currentAnimation);
            }, 2000);
        }

        function createMagicParticles() {
            const container = document.getElementById('particles');
            
            for (let i = 0; i < 20; i++) {
                setTimeout(() => {
                    const particle = document.createElement('div');
                    particle.className = 'particle';
                    particle.style.left = Math.random() * 100 + '%';
                    particle.style.animationDelay = Math.random() * 2 + 's';
                    particle.style.animationDuration = (2 + Math.random() * 2) + 's';
                    
                    // Rôzne farby častíc
                    const colors = ['#ffd700', '#87ceeb', '#ff69b4', '#98fb98'];
                    particle.style.background = colors[Math.floor(Math.random() * colors.length)];
                    
                    container.appendChild(particle);
                    
                    // Odstránenie častice po animácii
                    setTimeout(() => {
                        if (particle.parentNode) {
                            particle.parentNode.removeChild(particle);
                        }
                    }, 4000);
                }, i * 100);
            }
        }

        function clearMagicParticles() {
            const container = document.getElementById('particles');
            container.innerHTML = '';
        }

        // Inicializácia
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧙‍♂️ Čarodejnícka Alchymistická Hra - Character Demo načítaný');
            
            // Kontrola načítania SVG
            const svg = document.getElementById('wizardSvg');
            svg.addEventListener('load', function() {
                console.log('✅ Wizard SVG úspešne načítaný');
            });
            
            svg.addEventListener('error', function() {
                console.log('❌ Chyba pri načítaní SVG - skontrolujte cestu k wizard_puppet.svg');
            });
        });
    </script>
</body>
</html>
