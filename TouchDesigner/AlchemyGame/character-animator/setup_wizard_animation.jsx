/*
Čarodejnícka Alchymistická Hra - After Effects Animation Setup
ExtendScript pre automatické nastavenie animácie čarodejníka

Spustenie: File > Scripts > Run Script File...
*/

// Hlavná funkcia
function setupWizardAnimation() {
    app.beginUndoGroup("Setup Wizard Animation");
    
    try {
        // Vytvorenie novej kompozície
        var comp = createMainComposition();
        
        // Nastavenie základných animácií
        setupBasicAnimations(comp);
        
        // Vytvorenie preset animácií
        createAnimationPresets(comp);
        
        // Nastavenie exportných nastavení
        setupExportSettings();
        
        alert("Wizard animácia nastavená!\n\nKomponícia: 'Wizard_Main'\nDĺžka: 10 sekúnd\nFPS: 30\n\nPridajte váš Character Animator puppet do kompozície.");
        
    } catch (error) {
        alert("Chyba: " + error.toString());
    }
    
    app.endUndoGroup();
}

// Vytvorenie hlavnej kompozície
function createMainComposition() {
    var compName = "Wizard_Main";
    var width = 1920;
    var height = 1080;
    var duration = 10; // sekúnd
    var frameRate = 30;
    
    // Kontrola či kompozícia už existuje
    var existingComp = null;
    for (var i = 1; i <= app.project.items.length; i++) {
        if (app.project.items[i] instanceof CompItem && app.project.items[i].name === compName) {
            existingComp = app.project.items[i];
            break;
        }
    }
    
    var comp;
    if (existingComp) {
        comp = existingComp;
    } else {
        comp = app.project.items.addComp(compName, width, height, 1, duration, frameRate);
    }
    
    // Nastavenie pozadia
    var bgColor = [0.1, 0.1, 0.2]; // Tmavo modrá
    comp.bgColor = bgColor;
    
    return comp;
}

// Nastavenie základných animácií
function setupBasicAnimations(comp) {
    // Vytvorenie null objektu pre kontrolu
    var controlNull = comp.layers.addNull();
    controlNull.name = "Wizard_Controls";
    controlNull.label = 1; // Červená farba
    
    // Pridanie slider kontrolov
    addSliderControl(controlNull, "Breathing_Intensity", 50, 0, 100);
    addSliderControl(controlNull, "Head_Tilt", 0, -30, 30);
    addSliderControl(controlNull, "Eye_Direction_X", 0, -100, 100);
    addSliderControl(controlNull, "Eye_Direction_Y", 0, -100, 100);
    addSliderControl(controlNull, "Mouth_Expression", 0, 0, 4); // 0=neutral, 1=smile, 2=frown, 3=open, 4=surprised
    addSliderControl(controlNull, "Magic_Intensity", 0, 0, 100);
    
    // Pridanie checkbox kontrolov
    addCheckboxControl(controlNull, "Auto_Blink", true);
    addCheckboxControl(controlNull, "Auto_Breathing", true);
    addCheckboxControl(controlNull, "Magic_Particles", false);
    
    // Vytvorenie pozadia s gradientom
    createMagicalBackground(comp);
    
    // Pridanie svetelných efektov
    createLightingEffects(comp);
}

// Pridanie slider kontrolu
function addSliderControl(layer, name, defaultValue, minValue, maxValue) {
    var sliderEffect = layer.property("Effects").addProperty("Slider Control");
    sliderEffect.name = name;
    sliderEffect.property("Slider").setValue(defaultValue);
    
    // Poznámka: Min/Max hodnoty sa nastavujú manuálne v UI
    return sliderEffect;
}

// Pridanie checkbox kontrolu
function addCheckboxControl(layer, name, defaultValue) {
    var checkboxEffect = layer.property("Effects").addProperty("Checkbox Control");
    checkboxEffect.name = name;
    checkboxEffect.property("Checkbox").setValue(defaultValue ? 1 : 0);
    
    return checkboxEffect;
}

// Vytvorenie magického pozadia
function createMagicalBackground(comp) {
    // Gradient pozadie
    var bgSolid = comp.layers.addSolid([0.05, 0.05, 0.15], "Magic_Background", comp.width, comp.height, 1, comp.duration);
    bgSolid.moveToEnd();
    
    // Pridanie gradient efektu
    var gradientEffect = bgSolid.property("Effects").addProperty("4-Color Gradient");
    gradientEffect.property("Point 1").setValue([0, 0]);
    gradientEffect.property("Point 2").setValue([comp.width, 0]);
    gradientEffect.property("Point 3").setValue([0, comp.height]);
    gradientEffect.property("Point 4").setValue([comp.width, comp.height]);
    
    gradientEffect.property("Color 1").setValue([0.1, 0.05, 0.2]);
    gradientEffect.property("Color 2").setValue([0.2, 0.1, 0.3]);
    gradientEffect.property("Color 3").setValue([0.05, 0.1, 0.25]);
    gradientEffect.property("Color 4").setValue([0.15, 0.05, 0.2]);
    
    // Animácia gradientu
    var point1 = gradientEffect.property("Point 1");
    point1.setValueAtTime(0, [0, 0]);
    point1.setValueAtTime(comp.duration, [50, 50]);
    
    // Fractal Noise pre textúru
    var noiseEffect = bgSolid.property("Effects").addProperty("Fractal Noise");
    noiseEffect.property("Fractal Type").setValue(1); // Turbulent Smooth
    noiseEffect.property("Noise Type").setValue(1); // Soft Linear
    noiseEffect.property("Contrast").setValue(200);
    noiseEffect.property("Brightness").setValue(-50);
    noiseEffect.property("Blending Mode").setValue(9); // Overlay
    noiseEffect.property("Opacity").setValue(30);
    
    // Animácia noise
    var evolution = noiseEffect.property("Evolution");
    evolution.expression = "time * 20";
}

// Vytvorenie svetelných efektov
function createLightingEffects(comp) {
    // Svetelný solid
    var lightSolid = comp.layers.addSolid([1, 1, 1], "Magic_Light", comp.width, comp.height, 1, comp.duration);
    lightSolid.blendingMode = BlendingMode.ADD;
    lightSolid.opacity.setValue(20);
    
    // CC Light Burst efekt
    var lightBurst = lightSolid.property("Effects").addProperty("CC Light Burst 2.5");
    lightBurst.property("Center").setValue([comp.width/2, comp.height/2 - 100]);
    lightBurst.property("Intensity").setValue(50);
    lightBurst.property("Ray Length").setValue(200);
    lightBurst.property("Rays").setValue(12);
    lightBurst.property("Color").setValue([0.8, 0.9, 1]);
    
    // Animácia intenzity
    var intensity = lightBurst.property("Intensity");
    intensity.setValueAtTime(0, 30);
    intensity.setValueAtTime(2, 70);
    intensity.setValueAtTime(4, 40);
    intensity.setValueAtTime(6, 80);
    intensity.setValueAtTime(8, 35);
    intensity.setValueAtTime(10, 60);
    
    // Nastavenie keyframe interpolácie
    for (var i = 1; i <= intensity.numKeys; i++) {
        intensity.setInterpolationTypeAtKey(i, KeyframeInterpolationType.BEZIER);
        intensity.setTemporalEaseAtKey(i, [new KeyframeEase(0, 50)], [new KeyframeEase(0, 50)]);
    }
}

// Vytvorenie preset animácií
function createAnimationPresets(comp) {
    // Idle animácia
    createIdleAnimation(comp);
    
    // Casting spell animácia
    createSpellCastingAnimation(comp);
    
    // Talking animácia
    createTalkingAnimation(comp);
}

// Idle animácia
function createIdleAnimation(comp) {
    var idleComp = app.project.items.addComp("Wizard_Idle", comp.width, comp.height, 1, 5, comp.frameRate);
    idleComp.bgColor = comp.bgColor;
    
    // Poznámka: Sem sa pridá Character Animator puppet
    var textLayer = idleComp.layers.addText("ADD WIZARD PUPPET HERE\n\nIdle Animation:\n- Subtle breathing\n- Occasional blinks\n- Slight head movement");
    textLayer.property("Source Text").value.font = "Arial";
    textLayer.property("Source Text").value.fontSize = 24;
    textLayer.property("Source Text").value.fillColor = [1, 1, 1];
    textLayer.property("Source Text").value.justification = ParagraphJustification.CENTER;
    textLayer.position.setValue([comp.width/2, comp.height/2]);
}

// Spell casting animácia
function createSpellCastingAnimation(comp) {
    var spellComp = app.project.items.addComp("Wizard_SpellCasting", comp.width, comp.height, 1, 3, comp.frameRate);
    spellComp.bgColor = comp.bgColor;
    
    // Particle systém pre magické efekty
    var particleSolid = spellComp.layers.addSolid([1, 1, 1], "Magic_Particles", comp.width, comp.height, 1, spellComp.duration);
    
    var particleEffect = particleSolid.property("Effects").addProperty("CC Particle World");
    particleEffect.property("Birth Rate").setValue(50);
    particleEffect.property("Longevity (sec)").setValue(2);
    particleEffect.property("Producer").setValue([comp.width/2 + 200, comp.height/2 + 100]); // Pravá ruka
    particleEffect.property("Radius X").setValue(20);
    particleEffect.property("Radius Y").setValue(20);
    particleEffect.property("Velocity").setValue(100);
    particleEffect.property("Gravity").setValue(-50);
    
    // Farba častíc
    particleEffect.property("Birth Color").setValue([0.8, 0.9, 1]);
    particleEffect.property("Death Color").setValue([0.4, 0.6, 1]);
    
    var textLayer = spellComp.layers.addText("SPELL CASTING ANIMATION\n\nEffects:\n- Hand gestures\n- Particle magic\n- Glowing eyes\n- Dramatic pose");
    textLayer.property("Source Text").value.font = "Arial";
    textLayer.property("Source Text").value.fontSize = 20;
    textLayer.property("Source Text").value.fillColor = [1, 1, 1];
    textLayer.property("Source Text").value.justification = ParagraphJustification.CENTER;
    textLayer.position.setValue([comp.width/2, comp.height/2 - 200]);
}

// Talking animácia
function createTalkingAnimation(comp) {
    var talkComp = app.project.items.addComp("Wizard_Talking", comp.width, comp.height, 1, 4, comp.frameRate);
    talkComp.bgColor = comp.bgColor;
    
    var textLayer = talkComp.layers.addText("TALKING ANIMATION\n\nFeatures:\n- Lip sync ready\n- Head movements\n- Eye contact\n- Hand gestures\n\nImport audio for lip sync");
    textLayer.property("Source Text").value.font = "Arial";
    textLayer.property("Source Text").value.fontSize = 20;
    textLayer.property("Source Text").value.fillColor = [1, 1, 1];
    textLayer.property("Source Text").value.justification = ParagraphJustification.CENTER;
    textLayer.position.setValue([comp.width/2, comp.height/2]);
}

// Nastavenie exportných nastavení
function setupExportSettings() {
    // Vytvorenie render queue template
    // Poznámka: Render queue sa nastavuje manuálne
    
    // Vytvorenie poznámok pre export
    var exportNotes = [
        "EXPORT SETTINGS:",
        "",
        "Pre web/hru:",
        "- Format: H.264/MP4",
        "- Resolution: 1920x1080 alebo 1280x720",
        "- Frame Rate: 30 fps",
        "- Bitrate: 5-10 Mbps",
        "",
        "Pre vysokú kvalitu:",
        "- Format: ProRes 422",
        "- Resolution: 1920x1080",
        "- Frame Rate: 30 fps",
        "",
        "Pre GIF:",
        "- Format: Animated GIF",
        "- Resolution: 640x360",
        "- Frame Rate: 15 fps",
        "- Loop: Continuous"
    ];
    
    // Vytvorenie textového súboru s poznámkami
    var exportComp = app.project.items.addComp("EXPORT_NOTES", 800, 600, 1, 1, 30);
    var notesText = exportComp.layers.addText(exportNotes.join("\n"));
    notesText.property("Source Text").value.font = "Courier New";
    notesText.property("Source Text").value.fontSize = 16;
    notesText.property("Source Text").value.fillColor = [1, 1, 1];
    notesText.position.setValue([400, 300]);
}

// Spustenie hlavnej funkcie
setupWizardAnimation();
