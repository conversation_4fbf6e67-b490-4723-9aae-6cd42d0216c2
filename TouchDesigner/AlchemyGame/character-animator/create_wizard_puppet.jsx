/*
Čarodejnícka Alchymistická Hra - Character Animator Puppet Creator
Adobe Photoshop ExtendScript pre automatické vytvorenie bábky

Spustenie: File > Scripts > Browse... > vyberte tento súbor
*/

// Nastavenia dokumentu
var docWidth = 1920;
var docHeight = 1080;
var resolution = 72;

// Vytvorenie nového dokumentu
var doc = app.documents.add(docWidth, docHeight, resolution, "Wizard_Puppet", NewDocumentMode.RGB);

// Nastavenie jednotiek na pixely
app.preferences.rulerUnits = Units.PIXELS;

// Hlavná funkcia
function createWizardPuppet() {
    try {
        // Vytvorenie základnej štruktúry vrstiev
        createLayerStructure();
        
        // Pridanie tagov pre Character Animator
        addCharacterAnimatorTags();
        
        // Nastavenie anchor pointov
        setupAnchorPoints();
        
        alert("Wizard puppet štruktúra vytvorená!\n\nTeraz:\n1. Importujte váš PNG obrázok\n2. Rozdeľte ho na jednotlivé vrstvy\n3. Premiestnite časti do správnych skupín\n4. Uložte ako .psd súbor");
        
    } catch (error) {
        alert("Chyba: " + error.toString());
    }
}

// Vytvorenie štruktúry vrstiev
function createLayerStructure() {
    // Hlavné skupiny (s + prefix pre Character Animator)
    var mainGroups = [
        "+Head",
        "+Body", 
        "+Left_Arm",
        "+Right_Arm",
        "+Left_Hand",
        "+Right_Hand"
    ];
    
    // Vytvorenie hlavných skupín
    for (var i = 0; i < mainGroups.length; i++) {
        createLayerGroup(mainGroups[i]);
    }
    
    // Detailné vrstvy pre hlavu
    createHeadLayers();
    
    // Detailné vrstvy pre telo
    createBodyLayers();
    
    // Detailné vrstvy pre ruky
    createArmLayers();
}

// Vytvorenie skupiny vrstiev
function createLayerGroup(name) {
    var layerSet = doc.layerSets.add();
    layerSet.name = name;
    return layerSet;
}

// Vytvorenie prázdnej vrstvy
function createLayer(name, parent) {
    var layer = doc.artLayers.add();
    layer.name = name;
    
    if (parent) {
        layer.move(parent, ElementPlacement.INSIDE);
    }
    
    return layer;
}

// Detailné vrstvy pre hlavu
function createHeadLayers() {
    var headGroup = getLayerByName("+Head");
    
    // Základné časti tváre
    createLayer("Face", headGroup);
    createLayer("Hair", headGroup);
    createLayer("Ears", headGroup);
    
    // Oči s rôznymi stavmi
    var leftEyeGroup = createLayerGroup("+Left_Eye");
    leftEyeGroup.move(headGroup, ElementPlacement.INSIDE);
    createLayer("Left_Eye_Open", leftEyeGroup);
    createLayer("Left_Eye_Closed", leftEyeGroup);
    createLayer("Left_Eye_Blink", leftEyeGroup);
    
    var rightEyeGroup = createLayerGroup("+Right_Eye");
    rightEyeGroup.move(headGroup, ElementPlacement.INSIDE);
    createLayer("Right_Eye_Open", rightEyeGroup);
    createLayer("Right_Eye_Closed", rightEyeGroup);
    createLayer("Right_Eye_Blink", rightEyeGroup);
    
    // Ústa s rôznymi výrazmi
    var mouthGroup = createLayerGroup("Mouth");
    mouthGroup.move(headGroup, ElementPlacement.INSIDE);
    createLayer("Mouth_Neutral", mouthGroup);
    createLayer("Mouth_Smile", mouthGroup);
    createLayer("Mouth_Frown", mouthGroup);
    createLayer("Mouth_Open", mouthGroup);
    createLayer("Mouth_Surprised", mouthGroup);
    
    // Obočie
    createLayer("Left_Eyebrow", headGroup);
    createLayer("Right_Eyebrow", headGroup);
    
    // Nos
    createLayer("Nose", headGroup);
    
    // Brada a fúzy
    createLayer("Beard", headGroup);
    createLayer("Mustache", headGroup);
}

// Detailné vrstvy pre telo
function createBodyLayers() {
    var bodyGroup = getLayerByName("+Body");
    
    createLayer("Torso", bodyGroup);
    createLayer("Robe_Main", bodyGroup);
    createLayer("Robe_Details", bodyGroup);
    createLayer("Hood", bodyGroup);
    createLayer("Amulet", bodyGroup);
    createLayer("Belt", bodyGroup);
}

// Detailné vrstvy pre ruky
function createArmLayers() {
    // Ľavá ruka
    var leftArmGroup = getLayerByName("+Left_Arm");
    createLayer("Left_Upper_Arm", leftArmGroup);
    
    var leftForearmGroup = createLayerGroup("+Left_Forearm");
    leftForearmGroup.move(leftArmGroup, ElementPlacement.INSIDE);
    createLayer("Left_Lower_Arm", leftForearmGroup);
    
    var leftHandGroup = getLayerByName("+Left_Hand");
    createLayer("Left_Palm", leftHandGroup);
    createLayer("Left_Fingers", leftHandGroup);
    createLayer("Left_Ring", leftHandGroup);
    
    // Pravá ruka
    var rightArmGroup = getLayerByName("+Right_Arm");
    createLayer("Right_Upper_Arm", rightArmGroup);
    
    var rightForearmGroup = createLayerGroup("+Right_Forearm");
    rightForearmGroup.move(rightArmGroup, ElementPlacement.INSIDE);
    createLayer("Right_Lower_Arm", rightForearmGroup);
    
    var rightHandGroup = getLayerByName("+Right_Hand");
    createLayer("Right_Palm", rightHandGroup);
    createLayer("Right_Fingers", rightHandGroup);
}

// Pridanie tagov pre Character Animator
function addCharacterAnimatorTags() {
    // Tagy pre automatické správanie
    var tags = [
        // Dýchanie
        {layer: "+Body", tag: "Breathe"},
        
        // Mrmlanie očí
        {layer: "+Left_Eye", tag: "Blink"},
        {layer: "+Right_Eye", tag: "Blink"},
        
        // Sledovanie myši očami
        {layer: "+Left_Eye", tag: "Eye Gaze"},
        {layer: "+Right_Eye", tag: "Eye Gaze"},
        
        // Pohyb hlavy
        {layer: "+Head", tag: "Head Turner"},
        
        // Gestikulácia
        {layer: "+Left_Arm", tag: "Gesticulate"},
        {layer: "+Right_Arm", tag: "Gesticulate"},
        
        // Lip sync
        {layer: "Mouth", tag: "Lip Sync"}
    ];
    
    // Poznámka: Tagy sa pridávajú manuálne v Character Animator
    // Tento skript len pripraví štruktúru
}

// Nastavenie anchor pointov pre rotáciu
function setupAnchorPoints() {
    // Poznámka: Anchor pointy sa nastavujú v Character Animator
    // Odporúčané pozície:
    
    var anchorPoints = [
        {layer: "+Head", position: "center bottom"}, // Krk
        {layer: "+Left_Arm", position: "top center"}, // Rameno
        {layer: "+Right_Arm", position: "top center"}, // Rameno
        {layer: "+Left_Forearm", position: "top center"}, // Lakeť
        {layer: "+Right_Forearm", position: "top center"}, // Lakeť
        {layer: "+Left_Hand", position: "top center"}, // Zápästie
        {layer: "+Right_Hand", position: "top center"} // Zápästie
    ];
    
    // Vytvorenie guide vrstvy s poznámkami
    var guideLayer = createLayer("ANCHOR_POINTS_GUIDE");
    
    // Pridanie textových poznámok
    addTextNote("Head anchor: center bottom (neck)", 100, 100);
    addTextNote("Arm anchors: top center (shoulders)", 100, 130);
    addTextNote("Forearm anchors: top center (elbows)", 100, 160);
    addTextNote("Hand anchors: top center (wrists)", 100, 190);
}

// Pridanie textovej poznámky
function addTextNote(text, x, y) {
    var textLayer = doc.artLayers.add();
    textLayer.kind = LayerKind.TEXT;
    textLayer.name = "Note: " + text.substring(0, 20) + "...";
    
    var textItem = textLayer.textItem;
    textItem.contents = text;
    textItem.position = [x, y];
    textItem.size = 14;
    textItem.color = new SolidColor();
    textItem.color.rgb.red = 255;
    textItem.color.rgb.green = 0;
    textItem.color.rgb.blue = 0;
}

// Pomocná funkcia na nájdenie vrstvy podľa mena
function getLayerByName(name) {
    try {
        return doc.layerSets.getByName(name);
    } catch (e) {
        try {
            return doc.artLayers.getByName(name);
        } catch (e2) {
            return null;
        }
    }
}

// Vytvorenie návodu
function createInstructions() {
    var instructionsGroup = createLayerGroup("INSTRUCTIONS");
    
    var instructions = [
        "CHARACTER ANIMATOR PUPPET SETUP:",
        "",
        "1. Importujte váš wizard PNG obrázok",
        "2. Rozdeľte obrázok na jednotlivé časti:",
        "   - Hlavu (tvár, vlasy, oči, ústa)",
        "   - Telo (rúcho, amulet)",
        "   - Ruky (ramená, predlaktia, dlane)",
        "",
        "3. Premiestnite časti do správnych skupín",
        "4. Nastavte anchor pointy v Character Animator",
        "5. Pridajte tagy pre automatické správanie",
        "",
        "DÔLEŽITÉ:",
        "- Vrstvy s '+' sú riggable (pohyblivé)",
        "- Vytvorte rôzne výrazy úst pre lip sync",
        "- Pridajte zatvorené oči pre mrmlanie",
        "",
        "Po dokončení zmažte túto skupinu!"
    ];
    
    for (var i = 0; i < instructions.length; i++) {
        addTextNote(instructions[i], 50, 300 + (i * 25));
    }
}

// Spustenie hlavnej funkcie
createWizardPuppet();
createInstructions();
