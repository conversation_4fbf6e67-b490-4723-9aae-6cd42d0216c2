{"version": "4.0.0", "meta": {"name": "<PERSON><PERSON><PERSON>_<PERSON>", "description": "Live2D character configuration for realistic wizard with head movements, eye blinking, mouth movements, and idle animations", "author": "Character Animator", "created": "2025-07-18"}, "model": {"textureFiles": ["carodej.png"], "meshes": [{"id": "head", "vertices": [], "triangles": [], "uvs": []}, {"id": "eyes", "vertices": [], "triangles": [], "uvs": []}, {"id": "mouth", "vertices": [], "triangles": [], "uvs": []}, {"id": "body", "vertices": [], "triangles": [], "uvs": []}]}, "parameters": {"head_movements": {"ParamAngleX": {"id": "PARAM_ANGLE_X", "min": -30, "max": 30, "default": 0, "description": "Head horizontal rotation"}, "ParamAngleY": {"id": "PARAM_ANGLE_Y", "min": -30, "max": 30, "default": 0, "description": "Head vertical rotation"}, "ParamAngleZ": {"id": "PARAM_ANGLE_Z", "min": -10, "max": 10, "default": 0, "description": "Head tilt"}}, "eye_movements": {"ParamEyeLOpen": {"id": "PARAM_EYE_L_OPEN", "min": 0, "max": 1, "default": 1, "description": "Left eye openness"}, "ParamEyeROpen": {"id": "PARAM_EYE_R_OPEN", "min": 0, "max": 1, "default": 1, "description": "Right eye openness"}, "ParamEyeBallX": {"id": "PARAM_EYE_BALL_X", "min": -1, "max": 1, "default": 0, "description": "Eye ball horizontal movement"}, "ParamEyeBallY": {"id": "PARAM_EYE_BALL_Y", "min": -1, "max": 1, "default": 0, "description": "Eye ball vertical movement"}}, "mouth_movements": {"ParamMouthOpenY": {"id": "PARAM_MOUTH_OPEN_Y", "min": 0, "max": 1, "default": 0, "description": "Mouth vertical opening for speech"}, "ParamMouthForm": {"id": "PARAM_MOUTH_FORM", "min": -1, "max": 1, "default": 0, "description": "Mouth shape for different phonemes"}, "ParamMouthSize": {"id": "PARAM_MOUTH_SIZE", "min": -1, "max": 1, "default": 0, "description": "Mouth size adjustment"}}, "breathing": {"ParamBreath": {"id": "PARAM_BREATH", "min": 0, "max": 1, "default": 0, "description": "Breathing animation"}, "ParamBodyAngleX": {"id": "PARAM_BODY_ANGLE_X", "min": -10, "max": 10, "default": 0, "description": "Body sway for breathing"}}}, "animations": {"idle": {"name": "Idle Animation", "duration": 8000, "loop": true, "keyframes": [{"time": 0, "parameters": {"PARAM_BREATH": 0, "PARAM_ANGLE_X": 0, "PARAM_ANGLE_Y": 0, "PARAM_ANGLE_Z": 0, "PARAM_BODY_ANGLE_X": 0}}, {"time": 2000, "parameters": {"PARAM_BREATH": 1, "PARAM_ANGLE_X": 2, "PARAM_ANGLE_Y": 1, "PARAM_ANGLE_Z": 1, "PARAM_BODY_ANGLE_X": 2}}, {"time": 4000, "parameters": {"PARAM_BREATH": 0, "PARAM_ANGLE_X": -1, "PARAM_ANGLE_Y": -1, "PARAM_ANGLE_Z": -1, "PARAM_BODY_ANGLE_X": -1}}, {"time": 6000, "parameters": {"PARAM_BREATH": 0.5, "PARAM_ANGLE_X": 1, "PARAM_ANGLE_Y": 2, "PARAM_ANGLE_Z": 0.5, "PARAM_BODY_ANGLE_X": 1}}, {"time": 8000, "parameters": {"PARAM_BREATH": 0, "PARAM_ANGLE_X": 0, "PARAM_ANGLE_Y": 0, "PARAM_ANGLE_Z": 0, "PARAM_BODY_ANGLE_X": 0}}]}, "blink": {"name": "Eye Blink", "duration": 300, "loop": false, "keyframes": [{"time": 0, "parameters": {"PARAM_EYE_L_OPEN": 1, "PARAM_EYE_R_OPEN": 1}}, {"time": 100, "parameters": {"PARAM_EYE_L_OPEN": 0, "PARAM_EYE_R_OPEN": 0}}, {"time": 200, "parameters": {"PARAM_EYE_L_OPEN": 0, "PARAM_EYE_R_OPEN": 0}}, {"time": 300, "parameters": {"PARAM_EYE_L_OPEN": 1, "PARAM_EYE_R_OPEN": 1}}]}, "head_nod": {"name": "Head Nod", "duration": 1000, "loop": false, "keyframes": [{"time": 0, "parameters": {"PARAM_ANGLE_Y": 0}}, {"time": 300, "parameters": {"PARAM_ANGLE_Y": -15}}, {"time": 600, "parameters": {"PARAM_ANGLE_Y": 5}}, {"time": 1000, "parameters": {"PARAM_ANGLE_Y": 0}}]}, "head_shake": {"name": "Head Shake", "duration": 1200, "loop": false, "keyframes": [{"time": 0, "parameters": {"PARAM_ANGLE_X": 0}}, {"time": 200, "parameters": {"PARAM_ANGLE_X": -20}}, {"time": 400, "parameters": {"PARAM_ANGLE_X": 20}}, {"time": 600, "parameters": {"PARAM_ANGLE_X": -15}}, {"time": 800, "parameters": {"PARAM_ANGLE_X": 15}}, {"time": 1000, "parameters": {"PARAM_ANGLE_X": -5}}, {"time": 1200, "parameters": {"PARAM_ANGLE_X": 0}}]}, "head_tilt": {"name": "Occasional Head Tilt", "duration": 2000, "loop": false, "keyframes": [{"time": 0, "parameters": {"PARAM_ANGLE_Z": 0}}, {"time": 500, "parameters": {"PARAM_ANGLE_Z": 8}}, {"time": 1500, "parameters": {"PARAM_ANGLE_Z": 8}}, {"time": 2000, "parameters": {"PARAM_ANGLE_Z": 0}}]}}, "lip_sync": {"enabled": true, "phoneme_mapping": {"A": {"PARAM_MOUTH_OPEN_Y": 0.8, "PARAM_MOUTH_FORM": 0}, "E": {"PARAM_MOUTH_OPEN_Y": 0.4, "PARAM_MOUTH_FORM": 0.3}, "I": {"PARAM_MOUTH_OPEN_Y": 0.2, "PARAM_MOUTH_FORM": 0.8}, "O": {"PARAM_MOUTH_OPEN_Y": 0.6, "PARAM_MOUTH_FORM": -0.5}, "U": {"PARAM_MOUTH_OPEN_Y": 0.3, "PARAM_MOUTH_FORM": -0.8}, "N": {"PARAM_MOUTH_OPEN_Y": 0.1, "PARAM_MOUTH_FORM": 0.2}, "S": {"PARAM_MOUTH_OPEN_Y": 0.1, "PARAM_MOUTH_FORM": 0.6}, "T": {"PARAM_MOUTH_OPEN_Y": 0.2, "PARAM_MOUTH_FORM": 0.4}, "silence": {"PARAM_MOUTH_OPEN_Y": 0, "PARAM_MOUTH_FORM": 0}}}}